"""
Framework configurations for GAAPF.

This module defines the framework configurations and module structures
for the supported AI frameworks.
"""

from enum import Enum
from typing import Dict, List, Optional, Any


class SupportedFrameworks(str, Enum):
    """Supported AI frameworks in the system."""
    LANGCHAIN = "langchain"
    LANGGRAPH = "langgraph"
    CREWAI = "crewai"
    AUTOGEN = "autogen"
    LLAMAINDEX = "llamaindex"

    @classmethod
    def list_frameworks(cls) -> List[str]:
        """Get a list of all supported frameworks."""
        return [framework.value for framework in cls]


class ModuleDifficulty(str, Enum):
    """Module difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class ModuleType(str, Enum):
    """Module types for different learning approaches."""
    CONCEPT = "concept"          # Theoretical concepts
    TUTORIAL = "tutorial"        # Step-by-step guide
    PROJECT = "project"          # Hands-on project
    CHALLENGE = "challenge"      # Problem-solving challenge
    REFERENCE = "reference"      # Reference material


class FrameworkModule:
    """
    Framework module class for GAAPF.
    
    Represents a learning module for a specific framework.
    """
    
    def __init__(
        self,
        module_id: str,
        title: str,
        description: str,
        framework: SupportedFrameworks,
        difficulty: ModuleDifficulty,
        module_type: ModuleType,
        prerequisites: List[str] = None,
        topics: List[str] = None,
        estimated_minutes: int = 60,
        content_path: str = None
    ):
        """
        Initialize a framework module.
        
        Args:
            module_id: Unique module identifier
            title: Module title
            description: Module description
            framework: Parent framework
            difficulty: Module difficulty level
            module_type: Type of module
            prerequisites: List of prerequisite module IDs
            topics: List of topics covered
            estimated_minutes: Estimated completion time in minutes
            content_path: Path to module content
        """
        self.module_id = module_id
        self.title = title
        self.description = description
        
        # Ensure enum values are used
        if isinstance(framework, str):
            self.framework = SupportedFrameworks(framework)
        else:
            self.framework = framework
            
        if isinstance(difficulty, str):
            self.difficulty = ModuleDifficulty(difficulty)
        else:
            self.difficulty = difficulty
            
        if isinstance(module_type, str):
            self.module_type = ModuleType(module_type)
        else:
            self.module_type = module_type
            
        self.prerequisites = prerequisites or []
        self.topics = topics or []
        self.estimated_minutes = estimated_minutes
        self.content_path = content_path
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the module to a dictionary.
        
        Returns:
            Dictionary representation of the module
        """
        return {
            "module_id": self.module_id,
            "title": self.title,
            "description": self.description,
            "framework": self.framework.value,
            "difficulty": self.difficulty.value,
            "module_type": self.module_type.value,
            "prerequisites": self.prerequisites,
            "topics": self.topics,
            "estimated_minutes": self.estimated_minutes,
            "content_path": self.content_path
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FrameworkModule':
        """
        Create a module from a dictionary.
        
        Args:
            data: Dictionary representation of a module
            
        Returns:
            FrameworkModule instance
        """
        return cls(
            module_id=data["module_id"],
            title=data["title"],
            description=data["description"],
            framework=data["framework"],
            difficulty=data["difficulty"],
            module_type=data["module_type"],
            prerequisites=data.get("prerequisites", []),
            topics=data.get("topics", []),
            estimated_minutes=data.get("estimated_minutes", 60),
            content_path=data.get("content_path")
        )


class FrameworkConfig:
    """
    Framework configuration class for GAAPF.
    
    Represents the configuration for a supported framework.
    """
    
    def __init__(
        self,
        framework: SupportedFrameworks,
        name: str,
        description: str,
        version: str,
        modules: List[FrameworkModule] = None,
        learning_paths: Dict[str, List[str]] = None,
        resources: Dict[str, str] = None
    ):
        """
        Initialize a framework configuration.
        
        Args:
            framework: Framework identifier
            name: Framework name
            description: Framework description
            version: Framework version
            modules: List of framework modules
            learning_paths: Dictionary of learning paths (path_name -> list of module_ids)
            resources: Dictionary of additional resources (resource_name -> resource_url)
        """
        # Ensure enum value is used
        if isinstance(framework, str):
            self.framework = SupportedFrameworks(framework)
        else:
            self.framework = framework
            
        self.name = name
        self.description = description
        self.version = version
        self.modules = modules or []
        self.learning_paths = learning_paths or {}
        self.resources = resources or {}
        
        # Create module lookup for efficient access
        self.module_lookup = {m.module_id: m for m in self.modules}
        
    @staticmethod
    def get_available_frameworks() -> List[str]:
        """
        Get a list of available frameworks.
        
        Returns:
            List of framework names
        """
        return [framework.value for framework in SupportedFrameworks]
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the configuration to a dictionary.
        
        Returns:
            Dictionary representation of the configuration
        """
        return {
            "framework": self.framework.value,
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "modules": [m.to_dict() for m in self.modules],
            "learning_paths": self.learning_paths,
            "resources": self.resources
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FrameworkConfig':
        """
        Create a configuration from a dictionary.
        
        Args:
            data: Dictionary representation of a configuration
            
        Returns:
            FrameworkConfig instance
        """
        modules = [
            FrameworkModule.from_dict(m) for m in data.get("modules", [])
        ]
        
        return cls(
            framework=data["framework"],
            name=data["name"],
            description=data["description"],
            version=data["version"],
            modules=modules,
            learning_paths=data.get("learning_paths", {}),
            resources=data.get("resources", {})
        )
        
    def get_module(self, module_id: str) -> Optional[FrameworkModule]:
        """
        Get a module by ID.
        
        Args:
            module_id: Module identifier
            
        Returns:
            FrameworkModule if found, None otherwise
        """
        return self.module_lookup.get(module_id)
        
    def get_learning_path(self, path_name: str) -> List[FrameworkModule]:
        """
        Get modules in a learning path.
        
        Args:
            path_name: Learning path name
            
        Returns:
            List of modules in the path
        """
        if path_name not in self.learning_paths:
            return []
            
        module_ids = self.learning_paths[path_name]
        return [self.get_module(mid) for mid in module_ids if mid in self.module_lookup]


# Default configurations for supported frameworks
DEFAULT_FRAMEWORK_CONFIGS = {
    SupportedFrameworks.LANGCHAIN: FrameworkConfig(
        framework=SupportedFrameworks.LANGCHAIN,
        name="LangChain",
        description="A framework for building applications with language models",
        version="0.3.25+",
        modules=[
            FrameworkModule(
                module_id="lc_basics",
                title="LangChain Basics",
                description="Introduction to LangChain concepts and components",
                framework=SupportedFrameworks.LANGCHAIN,
                difficulty=ModuleDifficulty.BEGINNER,
                module_type=ModuleType.CONCEPT,
                topics=["LLMs", "Chains", "Prompts", "Memory"],
                estimated_minutes=45
            ),
            FrameworkModule(
                module_id="lc_chains",
                title="Working with Chains",
                description="Learn how to build and use chains in LangChain",
                framework=SupportedFrameworks.LANGCHAIN,
                difficulty=ModuleDifficulty.BEGINNER,
                module_type=ModuleType.TUTORIAL,
                prerequisites=["lc_basics"],
                topics=["SimpleChain", "SequentialChain", "RouterChain"],
                estimated_minutes=60
            ),
            # Additional modules would be defined here
        ],
        learning_paths={
            "beginner": ["lc_basics", "lc_chains"],
            "advanced": ["lc_basics", "lc_chains", "lc_agents", "lc_memory"]
        }
    ),
    
    SupportedFrameworks.LANGGRAPH: FrameworkConfig(
        framework=SupportedFrameworks.LANGGRAPH,
        name="LangGraph",
        description="A framework for building stateful, multi-agent applications",
        version="0.4.7+",
        modules=[
            FrameworkModule(
                module_id="lg_basics",
                title="LangGraph Basics",
                description="Introduction to LangGraph concepts and components",
                framework=SupportedFrameworks.LANGGRAPH,
                difficulty=ModuleDifficulty.INTERMEDIATE,
                module_type=ModuleType.CONCEPT,
                topics=["Graphs", "Nodes", "Edges", "State Management"],
                estimated_minutes=60
            ),
            # Additional modules would be defined here
        ],
        learning_paths={
            "beginner": ["lg_basics", "lg_simple_graph"],
            "advanced": ["lg_basics", "lg_simple_graph", "lg_multi_agent", "lg_advanced"]
        }
    )
}


def get_framework_config(framework: SupportedFrameworks) -> FrameworkConfig:
    """
    Get the configuration for a framework.
    
    Args:
        framework: Framework identifier
        
    Returns:
        Framework configuration
    """
    return DEFAULT_FRAMEWORK_CONFIGS.get(framework) 