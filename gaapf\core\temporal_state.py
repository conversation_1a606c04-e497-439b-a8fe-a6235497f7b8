"""
Temporal State Manager for GAAPF.

This module implements the Temporal Learning Optimization system,
which continuously monitors learning outcomes and adapts constellation
selection based on historical performance patterns.
"""

from typing import Dict, Tuple, List, Optional
import json
import os
import time
from pathlib import Path

from gaapf.core.constellation import ConstellationType


class EffectivenessMetrics:
    """Metrics for measuring learning effectiveness."""
    
    def __init__(self):
        self.comprehension_score = 0.0  # Understanding level
        self.engagement_score = 0.0     # Interaction and interest
        self.completion_rate = 0.0      # Task completion
        self.satisfaction_score = 0.0   # User satisfaction
        self.efficiency_score = 0.0     # Time efficiency
        self.retention_estimate = 0.0   # Knowledge retention
        
    def calculate_overall_score(self) -> float:
        """Calculate the overall effectiveness score."""
        # Weighted average of all metrics
        weights = {
            "comprehension": 0.25,
            "engagement": 0.20,
            "completion": 0.20,
            "satisfaction": 0.15,
            "efficiency": 0.10,
            "retention": 0.10
        }
        
        overall_score = (
            weights["comprehension"] * self.comprehension_score +
            weights["engagement"] * self.engagement_score +
            weights["completion"] * self.completion_rate +
            weights["satisfaction"] * self.satisfaction_score +
            weights["efficiency"] * self.efficiency_score +
            weights["retention"] * self.retention_estimate
        )
        
        return overall_score
    
    def to_dict(self) -> Dict[str, float]:
        """Convert metrics to dictionary."""
        return {
            "comprehension_score": self.comprehension_score,
            "engagement_score": self.engagement_score,
            "completion_rate": self.completion_rate,
            "satisfaction_score": self.satisfaction_score,
            "efficiency_score": self.efficiency_score,
            "retention_estimate": self.retention_estimate,
            "overall_score": self.calculate_overall_score()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, float]) -> 'EffectivenessMetrics':
        """Create metrics from dictionary."""
        metrics = cls()
        metrics.comprehension_score = data.get("comprehension_score", 0.0)
        metrics.engagement_score = data.get("engagement_score", 0.0)
        metrics.completion_rate = data.get("completion_rate", 0.0)
        metrics.satisfaction_score = data.get("satisfaction_score", 0.0)
        metrics.efficiency_score = data.get("efficiency_score", 0.0)
        metrics.retention_estimate = data.get("retention_estimate", 0.0)
        return metrics


class TemporalStateManager:
    """
    Manages temporal optimization for constellation selection.
    
    The TemporalStateManager is responsible for:
    1. Tracking learning effectiveness over time
    2. Recognizing patterns in user learning
    3. Optimizing constellation selection based on historical data
    4. Adapting to changing user needs and preferences
    """
    
    def __init__(self, data_path: Path = Path("data/temporal_patterns")):
        """
        Initialize the TemporalStateManager.
        
        Args:
            data_path: Path to store temporal pattern data
        """
        self.data_path = data_path
        self.patterns = self._load_patterns()
        
    def _load_patterns(self) -> Dict:
        """
        Load temporal patterns from storage.
        
        Returns:
            Dictionary of user patterns
        """
        if not self.data_path.exists():
            os.makedirs(self.data_path, exist_ok=True)
            return {}
            
        patterns = {}
        for file in self.data_path.glob("*.json"):
            try:
                with open(file, "r") as f:
                    patterns[file.stem] = json.load(f)
            except Exception as e:
                print(f"Error loading pattern file {file}: {e}")
        
        return patterns
    
    def _save_patterns(self):
        """Save temporal patterns to storage."""
        os.makedirs(self.data_path, exist_ok=True)
        
        for user_id, pattern in self.patterns.items():
            try:
                with open(self.data_path / f"{user_id}.json", "w") as f:
                    json.dump(pattern, f, indent=2)
            except Exception as e:
                print(f"Error saving pattern for user {user_id}: {e}")
    
    async def optimize_constellation_selection(
        self,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_context: Dict
    ) -> Tuple[ConstellationType, float]:
        """
        Determine the optimal constellation type based on patterns.
        
        Args:
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            session_context: Additional context for the session
            
        Returns:
            Tuple of (optimal constellation type, confidence score)
        """
        user_id = user_profile.get("user_id", "unknown_user")
        
        # If we have no data for this user, use a heuristic approach
        if user_id not in self.patterns or not self.patterns[user_id].get("effectiveness_history"):
            return await self._heuristic_selection(user_profile, framework, module_id)
        
        # Get user's pattern data
        user_patterns = self.patterns[user_id]
        
        # Find similar contexts in history
        similar_contexts = self._find_similar_contexts(
            user_patterns, framework, module_id, session_context
        )
        
        if not similar_contexts:
            # Fall back to heuristic if no similar contexts found
            return await self._heuristic_selection(user_profile, framework, module_id)
        
        # Calculate effectiveness scores for each constellation type
        constellation_scores = {}
        for context in similar_contexts:
            constellation_type = context["constellation_type"]
            effectiveness = context["effectiveness"]
            
            if constellation_type not in constellation_scores:
                constellation_scores[constellation_type] = []
                
            constellation_scores[constellation_type].append(effectiveness["overall_score"])
        
        # Find the constellation type with the highest average score
        best_constellation = None
        best_score = -1
        confidence = 0.0
        
        for constellation_type, scores in constellation_scores.items():
            avg_score = sum(scores) / len(scores)
            if avg_score > best_score:
                best_score = avg_score
                best_constellation = constellation_type
                confidence = min(len(scores) / 5.0, 1.0)  # Confidence based on sample size
        
        # Convert string back to enum
        best_constellation_enum = ConstellationType(best_constellation)
        
        return best_constellation_enum, confidence
    
    def _find_similar_contexts(
        self,
        user_patterns: Dict,
        framework: str,
        module_id: str,
        session_context: Dict
    ) -> List[Dict]:
        """
        Find contexts in history similar to the current one.
        
        Args:
            user_patterns: User's pattern data
            framework: Target framework
            module_id: Current learning module
            session_context: Additional context for the session
            
        Returns:
            List of similar historical contexts
        """
        similar_contexts = []
        
        for entry in user_patterns.get("effectiveness_history", []):
            # Check for exact framework and module matches
            if entry["framework"] == framework and entry["module_id"] == module_id:
                similar_contexts.append(entry)
                continue
                
            # Check for same framework but different module
            if entry["framework"] == framework:
                similar_contexts.append(entry)
                continue
        
        return similar_contexts
    
    async def _heuristic_selection(
        self,
        user_profile: Dict,
        framework: str,
        module_id: str
    ) -> Tuple[ConstellationType, float]:
        """
        Select constellation based on heuristics when no history is available.
        
        Args:
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            
        Returns:
            Tuple of (selected constellation type, confidence score)
        """
        # Extract relevant profile information
        experience_level = user_profile.get("programming_experience_years", 0)
        skill_level = user_profile.get("python_skill_level", "beginner")
        learning_style = user_profile.get("preferred_learning_style", "balanced")
        learning_pace = user_profile.get("learning_pace", "moderate")
        
        # Default to balanced approach
        constellation = ConstellationType.THEORY_PRACTICE_BALANCED
        confidence = 0.5  # Medium confidence for heuristic approach
        
        # Adjust based on experience and skill level
        if experience_level < 1 or skill_level == "beginner":
            constellation = ConstellationType.BASIC_LEARNING
            confidence = 0.7
        elif experience_level > 5 and skill_level in ["advanced", "expert"]:
            if learning_style == "theoretical":
                constellation = ConstellationType.KNOWLEDGE_INTENSIVE
                confidence = 0.6
            else:
                constellation = ConstellationType.HANDS_ON_FOCUSED
                confidence = 0.6
                
        # Adjust based on learning style
        if learning_style == "hands_on":
            constellation = ConstellationType.HANDS_ON_FOCUSED
            confidence = 0.7
        elif learning_style == "theoretical":
            constellation = ConstellationType.KNOWLEDGE_INTENSIVE
            confidence = 0.7
        elif learning_style == "guided":
            constellation = ConstellationType.GUIDED_LEARNING
            confidence = 0.7
            
        # Adjust based on learning pace
        if learning_pace == "slow" and constellation not in [ConstellationType.BASIC_LEARNING, ConstellationType.GUIDED_LEARNING]:
            constellation = ConstellationType.GUIDED_LEARNING
            confidence = 0.6
            
        return constellation, confidence
    
    async def update_effectiveness(
        self,
        user_id: str,
        framework: str,
        module_id: str,
        constellation_type: ConstellationType,
        metrics: EffectivenessMetrics,
        session_context: Dict = None
    ):
        """
        Update effectiveness metrics for a constellation.
        
        Args:
            user_id: User identifier
            framework: Target framework
            module_id: Current learning module
            constellation_type: Type of constellation used
            metrics: Effectiveness metrics
            session_context: Additional context for the session
        """
        if user_id not in self.patterns:
            self.patterns[user_id] = {
                "user_id": user_id,
                "effectiveness_history": []
            }
            
        # Create a new effectiveness entry
        entry = {
            "timestamp": time.time(),
            "framework": framework,
            "module_id": module_id,
            "constellation_type": constellation_type.value,
            "effectiveness": metrics.to_dict(),
            "context": session_context or {}
        }
        
        # Add to history
        self.patterns[user_id]["effectiveness_history"].append(entry)
        
        # Save updated patterns
        self._save_patterns()
        
    async def analyze_patterns(self, user_id: str) -> Dict:
        """
        Analyze learning patterns for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Analysis of user's learning patterns
        """
        if user_id not in self.patterns:
            return {"error": "No data available for user"}
            
        user_patterns = self.patterns[user_id]
        history = user_patterns.get("effectiveness_history", [])
        
        if not history:
            return {"error": "No history available for user"}
            
        # Calculate average effectiveness by constellation type
        constellation_effectiveness = {}
        for entry in history:
            constellation_type = entry["constellation_type"]
            if constellation_type not in constellation_effectiveness:
                constellation_effectiveness[constellation_type] = {
                    "count": 0,
                    "total_score": 0,
                    "scores": []
                }
                
            effectiveness = entry["effectiveness"]
            overall_score = effectiveness["overall_score"]
            
            constellation_effectiveness[constellation_type]["count"] += 1
            constellation_effectiveness[constellation_type]["total_score"] += overall_score
            constellation_effectiveness[constellation_type]["scores"].append(overall_score)
            
        # Calculate averages and trends
        analysis = {
            "user_id": user_id,
            "total_sessions": len(history),
            "constellation_performance": {},
            "recommendations": []
        }
        
        for constellation_type, data in constellation_effectiveness.items():
            avg_score = data["total_score"] / data["count"]
            scores = data["scores"]
            
            # Calculate trend (positive if scores are improving)
            trend = 0
            if len(scores) >= 3:
                # Simple linear regression slope calculation
                x = list(range(len(scores)))
                mean_x = sum(x) / len(x)
                mean_y = sum(scores) / len(scores)
                
                numerator = sum((x[i] - mean_x) * (scores[i] - mean_y) for i in range(len(scores)))
                denominator = sum((x[i] - mean_x) ** 2 for i in range(len(scores)))
                
                trend = numerator / denominator if denominator != 0 else 0
                
            analysis["constellation_performance"][constellation_type] = {
                "average_score": avg_score,
                "sessions": data["count"],
                "trend": trend
            }
            
        # Generate recommendations
        best_constellation = max(
            analysis["constellation_performance"].items(),
            key=lambda x: x[1]["average_score"]
        )[0]
        
        analysis["recommendations"].append({
            "type": "constellation_preference",
            "message": f"User performs best with {best_constellation} constellation",
            "confidence": 0.7
        })
        
        return analysis 