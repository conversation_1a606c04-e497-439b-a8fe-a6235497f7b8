"""
CLI application for GAAPF.

This module implements a command-line interface for interacting
with the GAAPF system.
"""

import asyncio
import os
import sys
import json
import uuid
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import questionary
from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

# Import GAAPF components
from gaapf.core.learning_hub import LearningHubCore
from gaapf.config.framework_configs import SupportedFrameworks
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from gaapf.config.env_config import get_config


class GAAPFCli:
    """
    Command-line interface for GAAPF.
    
    Provides a text-based interface for interacting with the GAAPF system.
    """
    
    def __init__(self, data_path: Path = Path("data")):
        """
        Initialize the CLI application.
        
        Args:
            data_path: Path to store data
        """
        self.console = Console()
        self.data_path = data_path
        self.learning_hub = None
        self.current_user_id = None
        self.current_session_id = None
        
    async def initialize(self):
        """Initialize the CLI application."""
        # Print welcome message
        self.console.print(Panel.fit(
            "Welcome to GAAPF - Guidance AI Agent for Python Framework",
            title="GAAPF CLI",
            border_style="blue"
        ))

        # Load and validate configuration
        config = get_config()
        config.print_configuration_status()

        validation = config.validate_configuration()
        if not validation["valid"]:
            self.console.print("\n❌ Configuration errors found. Please check your .env file.")
            self.console.print("Copy .env.example to .env and configure your API keys.")
            return False

        # Initialize the learning hub
        self.learning_hub = LearningHubCore(data_path=config.data_path)
        await self.learning_hub.initialize()

        # Initialize LLM using configuration
        try:
            self.llm = config.create_llm()
            # Determine which provider was actually used
            available_providers = config.get_available_llm_providers()
            if available_providers.get(config.default_llm_provider, False):
                actual_provider = config.default_llm_provider
            else:
                # Find the first available provider
                actual_provider = next((p for p, available in available_providers.items() if available), "unknown")

            self.console.print(f"✅ Using {actual_provider} LLM provider")
        except Exception as e:
            self.console.print(f"❌ Error creating LLM: {e}")
            return False

        self.learning_hub.llm = self.llm
        return True
        
    async def run(self):
        """Run the CLI application."""
        if not await self.initialize():
            return

        # Main menu loop
        while True:
            action = await self._show_main_menu()
            
            if action == "login":
                await self._login()
            elif action == "register":
                await self._register()
            elif action == "start_session":
                await self._start_session()
            elif action == "continue_session":
                await self._continue_session()
            elif action == "view_progress":
                await self._view_progress()
            elif action == "exit":
                self.console.print("Thank you for using GAAPF. Goodbye!")
                break
                
    async def _show_main_menu(self) -> str:
        """
        Show the main menu.
        
        Returns:
            Selected action
        """
        self.console.print("\n")
        
        # Show different options based on login state
        if self.current_user_id:
            result = await questionary.select(
                "What would you like to do?",
                choices=[
                    "Start a new learning session",
                    "Continue a previous session",
                    "View learning progress",
                    "Logout",
                    "Exit"
                ]
            ).ask_async()
            
            action_map = {
                "Start a new learning session": "start_session",
                "Continue a previous session": "continue_session",
                "View learning progress": "view_progress",
                "Logout": "logout",
                "Exit": "exit"
            }
            
            if result == "Logout":
                self.current_user_id = None
                self.console.print("Logged out successfully.")
                return await self._show_main_menu()
                
            return action_map.get(result, "exit")
        else:
            result = await questionary.select(
                "What would you like to do?",
                choices=[
                    "Login",
                    "Register",
                    "Exit"
                ]
            ).ask_async()
            
            action_map = {
                "Login": "login",
                "Register": "register",
                "Exit": "exit"
            }
            
            return action_map.get(result, "exit")
            
    async def _login(self):
        """Handle user login."""
        self.console.print("\n[bold]User Login[/bold]")
        
        # Get user profiles
        profiles_path = self.data_path / "user_profiles"
        if not profiles_path.exists():
            self.console.print("No users found. Please register first.")
            return
            
        # List available users
        user_files = list(profiles_path.glob("*.json"))
        if not user_files:
            self.console.print("No users found. Please register first.")
            return
            
        user_ids = [file.stem for file in user_files]
        
        # Let user select a profile
        user_id = await questionary.select(
            "Select a user profile:",
            choices=user_ids + ["Cancel"]
        ).ask_async()
        
        if user_id == "Cancel":
            return
            
        # Load the profile
        profile = await self.learning_hub.get_user_profile(user_id)
        if profile:
            self.current_user_id = user_id
            self.console.print(f"Welcome back, {user_id}!")
        else:
            self.console.print(f"Error loading user profile for {user_id}.")
            
    async def _register(self):
        """Handle user registration."""
        self.console.print("\n[bold]User Registration[/bold]")
        
        # Get user information
        user_id = await questionary.text("Enter a username:").ask_async()
        
        # Check if user already exists
        profiles_path = self.data_path / "user_profiles"
        if profiles_path.exists() and (profiles_path / f"{user_id}.json").exists():
            self.console.print(f"User {user_id} already exists. Please login instead.")
            return
            
        # Get programming experience
        experience_years = await questionary.select(
            "Years of programming experience:",
            choices=["0", "1", "2-3", "4-5", "6-10", "10+"]
        ).ask_async()
        
        # Convert to numeric value
        if experience_years == "0":
            experience_numeric = 0
        elif experience_years == "1":
            experience_numeric = 1
        elif experience_years == "2-3":
            experience_numeric = 2
        elif experience_years == "4-5":
            experience_numeric = 4
        elif experience_years == "6-10":
            experience_numeric = 6
        else:  # 10+
            experience_numeric = 10
            
        # Get Python skill level
        skill_level = await questionary.select(
            "Python skill level:",
            choices=[level.value for level in SkillLevel]
        ).ask_async()
        
        # Get learning preferences
        learning_pace = await questionary.select(
            "Preferred learning pace:",
            choices=[pace.value for pace in LearningPace]
        ).ask_async()
        
        learning_style = await questionary.select(
            "Preferred learning style:",
            choices=[style.value for style in LearningStyle]
        ).ask_async()
        
        # Create user profile
        profile = UserProfile(
            user_id=user_id,
            programming_experience_years=experience_numeric,
            python_skill_level=skill_level,
            learning_pace=learning_pace,
            preferred_learning_style=learning_style
        )
        
        # Save profile
        await self.learning_hub.save_user_profile(profile)
        
        self.current_user_id = user_id
        self.console.print(f"Welcome, {user_id}! Your profile has been created.")
        
    async def _start_session(self):
        """Start a new learning session."""
        if not self.current_user_id:
            self.console.print("Please login first.")
            return
            
        self.console.print("\n[bold]Start New Learning Session[/bold]")
        
        # Select framework
        framework = await questionary.select(
            "Select a framework to learn:",
            choices=[fw.value for fw in SupportedFrameworks]
        ).ask_async()
        
        # Get framework config
        framework_enum = SupportedFrameworks(framework)
        framework_config = self.learning_hub.constellation_manager.framework_configs.get(framework_enum)
        
        if not framework_config:
            self.console.print(f"Framework {framework} is not configured yet.")
            return
            
        # Select module
        module_choices = [
            f"{module.module_id}: {module.title} ({module.difficulty.value})"
            for module in framework_config.modules
        ]
        
        if not module_choices:
            self.console.print(f"No modules available for {framework}.")
            return
            
        module_selection = await questionary.select(
            "Select a module to learn:",
            choices=module_choices + ["Cancel"]
        ).ask_async()
        
        if module_selection == "Cancel":
            return
            
        module_id = module_selection.split(":")[0].strip()
        
        # Create session
        try:
            session_id = await self.learning_hub.create_session(
                user_id=self.current_user_id,
                framework=framework,
                module_id=module_id,
                llm=self.llm
            )
            
            self.current_session_id = session_id
            self.console.print(f"Session created: {session_id}")
            
            # Start conversation
            await self._run_conversation()
            
        except Exception as e:
            self.console.print(f"Error creating session: {e}")
            
    async def _continue_session(self):
        """Continue a previous session."""
        if not self.current_user_id:
            self.console.print("Please login first.")
            return
            
        self.console.print("\n[bold]Continue Previous Session[/bold]")
        self.console.print("This feature is not implemented yet.")
        
    async def _view_progress(self):
        """View learning progress."""
        if not self.current_user_id:
            self.console.print("Please login first.")
            return
            
        self.console.print("\n[bold]Learning Progress[/bold]")
        
        try:
            # Get progress data
            progress = await self.learning_hub.get_learning_progress(self.current_user_id)
            
            # Display progress
            table = Table(title=f"Learning Progress for {self.current_user_id}")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Sessions", str(progress["total_sessions"]))
            table.add_row("Frameworks Studied", ", ".join(progress["frameworks_studied"]))
            table.add_row("Modules Completed", str(progress["modules_completed"]))
            table.add_row("Average Effectiveness", f"{progress['average_effectiveness']:.2f}")
            
            self.console.print(table)
            
            # Display recommendations
            if progress.get("recommendations"):
                self.console.print("\n[bold]Recommendations:[/bold]")
                for rec in progress["recommendations"]:
                    self.console.print(f"- {rec['message']}")
                    
        except Exception as e:
            self.console.print(f"Error retrieving progress: {e}")
            
    async def _run_conversation(self):
        """Run a conversation session."""
        if not self.current_session_id:
            self.console.print("No active session.")
            return
            
        self.console.print("\n[bold]Learning Session[/bold]")
        self.console.print("Type 'exit' to end the session.")
        
        while True:
            try:
                # Get user input
                user_input = await questionary.text("You:").ask_async()
                
                # Handle cancellation or exit
                if user_input is None:
                    self.console.print("\nCancelled by user")
                    break
                    
                if user_input.lower() in ["exit", "quit", "bye"]:
                    # End session
                    await self.learning_hub.end_session(self.current_session_id)
                    self.current_session_id = None
                    self.console.print("Session ended.")
                    break
                    
                try:
                    # Process message
                    result = await self.learning_hub.process_message(
                        user_id=self.current_user_id,
                        message=user_input,
                        session_id=self.current_session_id
                    )
                    
                    # Display response
                    self.console.print("\n[bold]GAAPF:[/bold]")
                    self.console.print(Markdown(result["response"]))
                    self.console.print(f"[dim](via {', '.join(result['agent_path'])})[/dim]")
                    
                except Exception as e:
                    self.console.print(f"Error: {e}")
                    
            except KeyboardInterrupt:
                self.console.print("\nCancelled by user")
                break
            except Exception as e:
                self.console.print(f"\nError: {e}")
                break


async def main():
    """Main entry point for the CLI application."""
    parser = argparse.ArgumentParser(description="GAAPF CLI")
    parser.add_argument("--data-path", type=str, default="data", help="Path to data directory")
    args = parser.parse_args()
    
    app = GAAPFCli(data_path=Path(args.data_path))
    await app.run()


if __name__ == "__main__":
    asyncio.run(main()) 