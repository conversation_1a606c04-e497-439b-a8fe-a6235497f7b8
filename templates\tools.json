{"add": {"tool_name": "add", "arguments": {"a": "integer", "b": "integer"}, "return": "content_and_artifact", "docstring": "Add two numbers", "module_path": "__mcp__", "tool_type": "mcp", "tool_call_id": "tool_e25d9a0c-ec8c-483b-ada7-70779a23c668"}, "multiply": {"tool_name": "multiply", "arguments": {"a": "integer", "b": "integer"}, "return": "content_and_artifact", "docstring": "Multiply two numbers", "module_path": "__mcp__", "tool_type": "mcp", "tool_call_id": "tool_088b618f-093b-4d29-a8ed-1836cd474486"}, "get_framework_info": {"tool_name": "get_framework_info", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get detailed information about a specific framework including its description,\n    version, learning paths, and available resources. Use this when you need to provide information\n    about a framework's capabilities, structure, or general information.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_5a4c74cb-b63a-4e5e-8a8e-3b8838d8a37b", "is_runtime": true}, "get_module_info": {"tool_name": "get_module_info", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get detailed information about a specific learning module including its\n    description, difficulty level, topics covered, prerequisites, and estimated completion time.\n    Use this when you need to provide specific information about a learning module.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_2f5f0d98-9a3a-4550-b0cb-ca9da9cebfc0", "is_runtime": true}, "search_framework_content": {"tool_name": "search_framework_content", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Search for information across frameworks and modules based on a query.\n    This tool searches through framework names, descriptions, module titles, descriptions, and topics.\n    Use this when you need to find relevant information about specific concepts, features, or topics.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_63f3a01a-16a1-428c-833b-dbbc2dc59b43", "is_runtime": true}, "list_frameworks": {"tool_name": "list_frameworks", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get a list of all available frameworks in the system with their basic information.\n    Use this when you need to show what frameworks are available for learning.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_7c1b2976-57dc-4af2-b39c-68c7183be51c", "is_runtime": true}, "get_framework_modules": {"tool_name": "get_framework_modules", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get all learning modules available for a specific framework with their details.\n    Use this when you need to show what modules are available for learning in a particular framework.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_e3e693a5-5754-4e66-bf1f-775478979ae2", "is_runtime": true}}